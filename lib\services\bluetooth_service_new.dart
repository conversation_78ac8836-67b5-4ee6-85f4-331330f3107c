import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';

// Simple device model for scanning and saving
class BluetoothDeviceModel {
  final String name;
  final String address;
  final int rssi;
  final bool isSaved;
  final bool hasAlarm;
  final BluetoothDevice? device;
  final DateTime lastSeen;

  BluetoothDeviceModel({
    required this.name,
    required this.address,
    required this.rssi,
    this.isSaved = false,
    this.hasAlarm = false,
    this.device,
    DateTime? lastSeen,
  }) : lastSeen = lastSeen ?? DateTime.now();

  BluetoothDeviceModel copyWith({
    String? name,
    String? address,
    int? rssi,
    bool? isSaved,
    bool? hasAlarm,
    BluetoothDevice? device,
    DateTime? lastSeen,
  }) {
    return BluetoothDeviceModel(
      name: name ?? this.name,
      address: address ?? this.address,
      rssi: rssi ?? this.rssi,
      isSaved: isSaved ?? this.isSaved,
      hasAlarm: hasAlarm ?? this.hasAlarm,
      device: device ?? this.device,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  factory BluetoothDeviceModel.fromScanResult(ScanResult result) {
    return BluetoothDeviceModel(
      name: result.device.platformName.isNotEmpty
          ? result.device.platformName
          : result.advertisementData.advName.isNotEmpty
              ? result.advertisementData.advName
              : 'Unknown Device',
      address: result.device.remoteId.str,
      rssi: result.rssi,
      device: result.device,
    );
  }
}

class BluetoothService extends ChangeNotifier {
  final List<BluetoothDeviceModel> _scannedDevices = [];
  final List<BluetoothDeviceModel> _savedDevices = [];
  final Map<String, bool> _alarmSettings = {};
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanSubscription;
  Timer? _scanTimer;
  Timer? _deviceMonitorTimer;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isScanning = false;
  bool _isInitialized = false;
  String _statusMessage = 'Initializing...';
  int _minDbm = -80;
  int _maxDbm = -30;

  // Getters
  List<BluetoothDeviceModel> get devices => _scannedDevices;
  List<BluetoothDeviceModel> get savedDevices => _savedDevices;
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  String get statusMessage => _statusMessage;
  int get minDbm => _minDbm;
  int get maxDbm => _maxDbm;

  bool hasAlarm(String address) => _alarmSettings[address] ?? false;
  bool isSaved(String address) =>
      _savedDevices.any((d) => d.address == address);

  // Set dBm range for filtering
  void setDbmRange(int minDbm, int maxDbm) {
    _minDbm = minDbm;
    _maxDbm = maxDbm;
    notifyListeners();
  }

  // Initialize Bluetooth
  Future<void> initialize() async {
    try {
      _statusMessage = 'Checking Bluetooth support...';
      notifyListeners();

      if (await FlutterBluePlus.isSupported == false) {
        _statusMessage = 'Bluetooth not supported';
        _isInitialized = true;
        notifyListeners();
        return;
      }

      _statusMessage = 'Requesting permissions...';
      notifyListeners();

      final permissionsGranted = await _requestPermissions();
      if (!permissionsGranted) {
        _statusMessage = 'Bluetooth permissions denied';
        _isInitialized = true;
        notifyListeners();
        return;
      }

      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        _handleAdapterStateChange(state);
      });

      await _loadSavedDevices();
      await _loadAlarmSettings();

      _isInitialized = true;
      _statusMessage = 'Ready to scan';
      notifyListeners();

      // Start automatic scanning
      _startAutomaticScanning();
    } catch (e) {
      _statusMessage = 'Error: $e';
      _isInitialized = true;
      debugPrint('Bluetooth initialization error: $e');
      notifyListeners();
    }
  }

  // Request Bluetooth permissions
  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      try {
        final permissions = [
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.location,
        ];

        final statuses = await permissions.request();
        return statuses.values
            .every((status) => status == PermissionStatus.granted);
      } catch (e) {
        debugPrint('Error requesting permissions: $e');
        return false;
      }
    }
    return true;
  }

  // Handle adapter state changes
  void _handleAdapterStateChange(BluetoothAdapterState state) {
    switch (state) {
      case BluetoothAdapterState.on:
        _statusMessage = 'Bluetooth is on - Ready to scan';
        break;
      case BluetoothAdapterState.off:
        _statusMessage = 'Bluetooth is off - Please enable Bluetooth';
        _scannedDevices.clear();
        break;
      default:
        _statusMessage = 'Bluetooth state: $state';
    }
    notifyListeners();
  }

  // Load saved devices from SharedPreferences
  Future<void> _loadSavedDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedDevicesJson = prefs.getStringList('saved_devices') ?? [];

      _savedDevices.clear();
      for (final deviceJson in savedDevicesJson) {
        final parts = deviceJson.split('|');
        if (parts.length >= 2) {
          _savedDevices.add(BluetoothDeviceModel(
            name: parts[0],
            address: parts[1],
            rssi: 0,
            isSaved: true,
          ));
        }
      }
      debugPrint('Loaded ${_savedDevices.length} saved devices');
    } catch (e) {
      debugPrint('Error loading saved devices: $e');
    }
  }

  // Load alarm settings
  Future<void> _loadAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('alarm_'));
      for (final key in keys) {
        final address = key.substring(6);
        _alarmSettings[address] = prefs.getBool(key) ?? false;
      }
    } catch (e) {
      debugPrint('Error loading alarm settings: $e');
    }
  }

  // Start automatic scanning every 10 seconds
  void _startAutomaticScanning() {
    _scanTimer?.cancel();
    _scanTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (!_isScanning) {
        startScan();
      }
    });

    // Start device monitoring for saved devices with alarms
    _deviceMonitorTimer?.cancel();
    _deviceMonitorTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkSavedDevicesPresence();
    });
  }

  // Start scanning for 10 seconds
  Future<void> startScan() async {
    if (_isScanning) return;

    try {
      final adapterState = await FlutterBluePlus.adapterState.first.timeout(
        const Duration(seconds: 2),
        onTimeout: () => BluetoothAdapterState.unknown,
      );

      if (adapterState != BluetoothAdapterState.on) {
        _statusMessage = 'Bluetooth must be turned on to scan';
        notifyListeners();
        return;
      }

      _isScanning = true;
      _statusMessage = 'Scanning for devices...';
      _scannedDevices.clear();
      notifyListeners();

      _scanSubscription?.cancel();
      _scanSubscription = FlutterBluePlus.scanResults.listen((results) {
        for (final result in results) {
          // Filter by dBm range
          if (result.rssi >= _minDbm && result.rssi <= _maxDbm) {
            _addOrUpdateScannedDevice(result);
          }
        }
      });

      await FlutterBluePlus.startScan(timeout: const Duration(seconds: 10));
      await Future.delayed(const Duration(seconds: 10));

      _isScanning = false;
      _statusMessage = 'Found ${_scannedDevices.length} devices in range';
      notifyListeners();
    } catch (e) {
      _isScanning = false;
      _statusMessage = 'Scan error: $e';
      debugPrint('Scan error: $e');
      notifyListeners();
    }
  }

  // Add or update scanned device
  void _addOrUpdateScannedDevice(ScanResult result) {
    final address = result.device.remoteId.str;
    final existingIndex =
        _scannedDevices.indexWhere((d) => d.address == address);

    final deviceModel = BluetoothDeviceModel.fromScanResult(result).copyWith(
      isSaved: isSaved(address),
      hasAlarm: hasAlarm(address),
    );

    if (existingIndex >= 0) {
      _scannedDevices[existingIndex] = deviceModel;
    } else {
      _scannedDevices.add(deviceModel);
    }
    notifyListeners();
  }

  // Save a device
  Future<void> saveDevice(String address, String name) async {
    try {
      // Add to saved devices if not already saved
      if (!isSaved(address)) {
        _savedDevices.add(BluetoothDeviceModel(
          name: name,
          address: address,
          rssi: 0,
          isSaved: true,
        ));

        // Save to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        final savedDevicesJson =
            _savedDevices.map((d) => '${d.name}|${d.address}').toList();
        await prefs.setStringList('saved_devices', savedDevicesJson);

        // Update scanned devices to reflect saved status
        final scannedIndex =
            _scannedDevices.indexWhere((d) => d.address == address);
        if (scannedIndex >= 0) {
          _scannedDevices[scannedIndex] =
              _scannedDevices[scannedIndex].copyWith(isSaved: true);
        }

        notifyListeners();
        debugPrint('Device $name saved for monitoring');
      }
    } catch (e) {
      debugPrint('Error saving device: $e');
    }
  }

  // Remove a saved device
  Future<void> removeSavedDevice(String address) async {
    try {
      _savedDevices.removeWhere((d) => d.address == address);
      _alarmSettings.remove(address);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final savedDevicesJson =
          _savedDevices.map((d) => '${d.name}|${d.address}').toList();
      await prefs.setStringList('saved_devices', savedDevicesJson);
      await prefs.remove('alarm_$address');

      // Update scanned devices to reflect unsaved status
      final scannedIndex =
          _scannedDevices.indexWhere((d) => d.address == address);
      if (scannedIndex >= 0) {
        _scannedDevices[scannedIndex] = _scannedDevices[scannedIndex].copyWith(
          isSaved: false,
          hasAlarm: false,
        );
      }

      notifyListeners();
      debugPrint('Device removed from monitoring');
    } catch (e) {
      debugPrint('Error removing saved device: $e');
    }
  }

  // Toggle alarm for a saved device
  Future<void> toggleAlarm(String address) async {
    if (!isSaved(address)) return;

    try {
      _alarmSettings[address] = !(_alarmSettings[address] ?? false);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('alarm_$address', _alarmSettings[address]!);

      // Update both saved and scanned devices
      final savedIndex = _savedDevices.indexWhere((d) => d.address == address);
      if (savedIndex >= 0) {
        _savedDevices[savedIndex] = _savedDevices[savedIndex].copyWith(
          hasAlarm: _alarmSettings[address],
        );
      }

      final scannedIndex =
          _scannedDevices.indexWhere((d) => d.address == address);
      if (scannedIndex >= 0) {
        _scannedDevices[scannedIndex] = _scannedDevices[scannedIndex].copyWith(
          hasAlarm: _alarmSettings[address],
        );
      }

      notifyListeners();
      debugPrint(
          'Alarm ${_alarmSettings[address]! ? 'enabled' : 'disabled'} for device');
    } catch (e) {
      debugPrint('Error toggling alarm: $e');
    }
  }

  // Check if saved devices with alarms are still present
  void _checkSavedDevicesPresence() {
    final savedDevicesWithAlarms =
        _savedDevices.where((d) => d.hasAlarm).toList();

    for (final savedDevice in savedDevicesWithAlarms) {
      // Check if device is currently in scanned devices (detected)
      final isDetected =
          _scannedDevices.any((d) => d.address == savedDevice.address);

      if (!isDetected) {
        // Device not detected - trigger alarm
        _triggerAlarm(savedDevice);
      }
    }
  }

  // Trigger alarm when device is not detected
  void _triggerAlarm(BluetoothDeviceModel device) {
    debugPrint('🚨 ALARM: Device ${device.name} not detected!');

    // Play system alert sound
    SystemSound.play(SystemSoundType.alert);

    // You can add more alarm actions here like:
    // - Show notification
    // - Vibrate device
    // - Play custom sound
  }

  @override
  void dispose() {
    _scanSubscription?.cancel();
    _adapterStateSubscription?.cancel();
    _scanTimer?.cancel();
    _deviceMonitorTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }
}
